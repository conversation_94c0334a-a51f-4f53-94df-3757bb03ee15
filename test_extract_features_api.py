#!/usr/bin/env python3
"""
测试特征提取接口的脚本
"""

import requests
import os
import json

def test_extract_features_api():
    """
    测试特征提取接口（完整版）
    """
    # API端点
    url = "http://localhost:5000/api/extract_features/multiple"
    
    # 固定的测试图像路径
    test_image_path = r"E:\庄志轩\20250822092434_11.jpg"

    # 检查测试图像是否存在
    if not os.path.exists(test_image_path):
        print(f"❌ 测试图像不存在: {test_image_path}")
        print("请确保图像文件存在，或修改 test_image_path 变量为正确的路径")
        return False

    try:
        # 准备文件上传
        with open(test_image_path, 'rb') as f:
            files = {'image': (os.path.basename(test_image_path), f, 'image/jpeg')}

            print(f"正在测试特征提取接口...")
            print(f"上传图像: {test_image_path}")
            
            # 发送POST请求（增加超时时间）
            response = requests.post(url, files=files, timeout=300)
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 接口调用成功!")
                print(f"📊 处理状态: {result.get('status')}")
                print(f"💬 处理消息: {result.get('message')}")
                print(f"📷 原始文件名: {result.get('original_filename')}")
                print(f"🔄 预处理文件夹: {result.get('preprocessed_folder')}")
                print(f"🎯 特征提取文件夹: {result.get('features_folder')}")
                print(f"📁 生成文件数量: {result.get('total_files')}")
                print(f"🆔 处理ID: {result.get('processing_id')}")

                # 显示生成的文件列表
                generated_files = result.get('generated_files', [])
                if generated_files:
                    print(f"\n📋 生成的文件详情 (共 {len(generated_files)} 个):")

                    # 按类型分组显示
                    preprocessed_files = [f for f in generated_files if f.get('type') == 'preprocessed']
                    feature_files = [f for f in generated_files if f.get('type') == 'features']

                    if preprocessed_files:
                        print(f"\n  🔄 预处理文件 ({len(preprocessed_files)} 个):")
                        for file_info in preprocessed_files:
                            print(f"    ├── {file_info.get('filename')}")
                            print(f"    │   路径: {file_info.get('path')}")

                    if feature_files:
                        print(f"\n  🎯 特征提取文件 ({len(feature_files)} 个):")
                        for file_info in feature_files:
                            print(f"    ├── {file_info.get('filename')}")
                            print(f"    │   路径: {file_info.get('path')}")

                # 显示文件类型统计
                if generated_files:
                    jpg_count = len([f for f in generated_files if f.get('filename', '').lower().endswith(('.jpg', '.jpeg'))])
                    png_count = len([f for f in generated_files if f.get('filename', '').lower().endswith('.png')])
                    json_count = len([f for f in generated_files if f.get('filename', '').lower().endswith('.json')])

                    print(f"\n📊 文件类型统计:")
                    print(f"    🖼️  图像文件: {jpg_count}")
                    print(f"    📈 图表文件: {png_count}")
                    print(f"    📄 数据文件: {json_count}")

                return True
            else:
                print("❌ 接口调用失败!")
                try:
                    error_info = response.json()
                    print(f"错误信息: {error_info.get('message', '未知错误')}")
                except:
                    print(f"响应内容: {response.text}")
                return False
                
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保Flask服务器正在运行")
        print("启动命令: python algorithm_service/algorithm_service/app.py")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时! 处理时间过长")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        return False

def test_api_without_file():
    """
    测试没有文件的情况
    """
    url = "http://localhost:5000/api/extract_features/multiple"
    
    try:
        response = requests.post(url, timeout=10)
        print(f"\n测试无文件请求 - 状态码: {response.status_code}")
        
        if response.status_code == 400:
            result = response.json()
            print(f"✅ 正确返回错误: {result.get('message')}")
            return True
        else:
            print("❌ 应该返回400错误")
            return False
            
    except Exception as e:
        print(f"❌ 测试无文件请求失败: {str(e)}")
        return False



if __name__ == "__main__":
    print("=" * 50)
    print("特征提取接口测试")
    print("=" * 50)

    # 测试正常情况
    print("\n1. 测试特征提取接口...")
    success1 = test_extract_features_api()

    # # 测试异常情况
    # print("\n2. 测试异常情况...")
    # success2 = test_api_without_file()

    print("\n" + "=" * 50)
    if success1:
        print("✅ 已完成可见光特征提取!")
    # if success1 and success2:
    #     print("✅ 所有测试通过!")
    # else:
    #     print("❌ 部分测试失败!")
    print("=" * 50)