#!/usr/bin/env python3
"""
测试特征提取接口的脚本
"""

import requests
import os
import json

def test_extract_features_api():
    """
    测试特征提取接口（完整版）
    """
    # API端点
    url = "http://localhost:5000/api/extract_features/multiple"
    
    # 测试图像路径（可以根据实际情况修改）
    test_image_paths = [
        r"datasets\image\可见光\vis_00215.jpg",
        r"E:\z30\搬宿舍\DSC_6954.JPG"
    ]

    # 找到第一个存在的测试图像
    test_image_path = None
    for path in test_image_paths:
        if os.path.exists(path):
            test_image_path = path
            break

    # 检查测试图像是否存在
    if not test_image_path:
        print("测试图像不存在，请检查以下路径:")
        for path in test_image_paths:
            print(f"  - {path}")
        print("请确保至少有一个测试图像存在，或在脚本中添加你的图像路径")
        return False
    
    try:
        # 准备文件上传
        with open(test_image_path, 'rb') as f:
            files = {'image': (os.path.basename(test_image_path), f, 'image/jpeg')}
            
            print(f"正在测试特征提取接口...")
            print(f"上传图像: {test_image_path}")
            
            # 发送POST请求（增加超时时间）
            response = requests.post(url, files=files, timeout=300)
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 接口调用成功!")
                print(f"处理状态: {result.get('status')}")
                print(f"处理消息: {result.get('message')}")
                print(f"原始文件名: {result.get('original_filename')}")
                print(f"预处理文件夹: {result.get('preprocessed_folder')}")
                print(f"特征提取文件夹: {result.get('features_folder')}")
                print(f"生成文件数量: {result.get('total_files')}")
                print(f"处理ID: {result.get('processing_id')}")
                
                # 显示生成的文件列表
                generated_files = result.get('generated_files', [])
                if generated_files:
                    print("\n生成的文件:")
                    for file_info in generated_files:
                        print(f"  - 类型: {file_info.get('type')}, 文件名: {file_info.get('filename')}")
                
                return True
            else:
                print("❌ 接口调用失败!")
                try:
                    error_info = response.json()
                    print(f"错误信息: {error_info.get('message', '未知错误')}")
                except:
                    print(f"响应内容: {response.text}")
                return False
                
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保Flask服务器正在运行")
        print("启动命令: python algorithm_service/algorithm_service/app.py")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时! 处理时间过长")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        return False

def test_api_without_file():
    """
    测试没有文件的情况
    """
    url = "http://localhost:5000/api/extract_features/multiple"
    
    try:
        response = requests.post(url, timeout=10)
        print(f"\n测试无文件请求 - 状态码: {response.status_code}")
        
        if response.status_code == 400:
            result = response.json()
            print(f"✅ 正确返回错误: {result.get('message')}")
            return True
        else:
            print("❌ 应该返回400错误")
            return False
            
    except Exception as e:
        print(f"❌ 测试无文件请求失败: {str(e)}")
        return False



if __name__ == "__main__":
    print("=" * 50)
    print("特征提取接口测试")
    print("=" * 50)

    # 测试正常情况
    print("\n1. 测试特征提取接口...")
    success1 = test_extract_features_api()

    # 测试异常情况
    print("\n2. 测试异常情况...")
    success2 = test_api_without_file()

    print("\n" + "=" * 50)
    if success1 and success2:
        print("✅ 所有测试通过!")
    else:
        print("❌ 部分测试失败!")
    print("=" * 50)
