from flask import Blueprint, request, jsonify, send_file
from io import BytesIO
from PIL import Image

filter_features_bp = Blueprint('filter_features', __name__)

@filter_features_bp.route('/visible_light', methods=['POST'])
def filter_features_visible_light():
    """
    可见光特征筛选接口

    """
    try:
        # 检查请求中是否包含文件
        if 'image' not in request.files:
            return jsonify({
                "status": "error",
                "message": "请求中缺少图像文件"
            }), 400

        # 获取上传的图像文件
        file = request.files['image']

        # 检查文件是否有文件名
        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "未选择图像文件"
            }), 400

        # 读取图像
        img = Image.open(file.stream).convert('RGB')

        #添加实际算法处理!!!

        # 将处理后的图像保存到内存中
        buffer = BytesIO()
        img.save(buffer, format='JPEG')
        buffer.seek(0)

        # 返回处理后的图像文件
        return send_file(
            buffer,
            mimetype='image/jpeg',
        )

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

#可以按照可见光、红外和SAR分类写三个服务函数