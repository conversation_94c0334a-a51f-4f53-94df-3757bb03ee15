import requests
import json

API_URL = "http://12c96f36.r17.cpolar.top"


# API_URL = "http://5a3bd8f4.r7.cpolar.top/shijian/day_night"

def test_api_with_file_path(file_path):
    """
    测试 API：输入文件绝对路径，获取检测结果
    :param file_path: 本地文件的绝对路径（如 "C:/images/test.jpg"）
    """
    # 1. 构造请求数据（文件路径作为参数）
    request_data = {
        "file_path": file_path  # 关键参数：文件绝对路径
    }

    try:
        # 2. 发送 POST 请求（根据 API 实际需求调整 method）
        response = requests.post(
            API_URL,
            json=request_data,
            headers={"Content-Type": "application/json"}
        )

        # 3. 处理响应
        response.raise_for_status()  # 检查 HTTP 状态码
        result = response.json()

        # 4. 打印结果
        print("=== API 测试结果 ===")
        print(f"请求路径: {API_URL}")
        print(f"输入文件路径: {file_path}")
        print(f"响应状态: {result.get('status', '未知')}")
        print(f"检测结果: {json.dumps(result.get('detection_result'), indent=2, ensure_ascii=False)}")

        # 5. （可选）断言验证结果
        assert result.get("status") == "success", "API 应返回 success 状态"
        assert "detection_result" in result, "响应应包含 detection_result 字段"

    except requests.RequestException as e:
        # 网络/HTTP 错误
        print(f"API 调用失败: {str(e)}")
    except AssertionError as e:
        # 结果不符合预期
        print(f"结果验证失败: {str(e)}")


if __name__ == "__main__":
    TEST_FILE_PATH = "E:\\z30\\DSC_4776.JPG"
    test_api_with_file_path(TEST_FILE_PATH)