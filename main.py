from model.特征提取.可见光.feature_img import *

if __name__ == "__main__":
    # 单张图片（视频）
    img = r"datasets\image\可见光\vis_00215.jpg"
    output_folder1 = r"Results\可见光\特征提取模块\图像预处理"
    output_folder2 = r"Results\可见光\特征提取模块\特征提取"
    output_folder3 = r"Results\可见光\特征提取模块\识别有效性筛选" 

#########调用方法##########
    # 图像
    ImageProcessor.process_ImageProcessor(img, output_folder1)#预处理
    FeatureExtractor.process_FeatureExtractor(output_folder1, output_folder2) #特征提取


