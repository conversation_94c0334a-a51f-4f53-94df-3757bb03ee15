import os
import time
from datetime import datetime
from model.特征提取.可见光.feature_img import *

def print_header(title):
    """打印格式化的标题"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_section(title):
    """打印格式化的章节标题"""
    print(f"\n{'='*20} {title} {'='*20}")

def print_result_summary(results_files, output_folder1, output_folder2):
    """打印结果摘要"""
    print_section("处理结果摘要")

    # 统计预处理文件
    preprocess_files = []
    feature_files = []

    if os.path.exists(output_folder1):
        preprocess_files = [f for f in os.listdir(output_folder1)
                          if f.lower().endswith(('.jpg', '.jpeg', '.png', '.json'))]

    if os.path.exists(output_folder2):
        feature_files = [f for f in os.listdir(output_folder2)
                        if f.lower().endswith(('.jpg', '.jpeg', '.png', '.json'))]

    print(f"📁 预处理结果目录: {output_folder1}")
    print(f"   生成文件数量: {len(preprocess_files)}")
    if preprocess_files:
        for file in sorted(preprocess_files):
            print(f"   ├── {file}")

    print(f"\n📁 特征提取结果目录: {output_folder2}")
    print(f"   生成文件数量: {len(feature_files)}")
    if feature_files:
        for file in sorted(feature_files):
            print(f"   ├── {file}")

    print(f"\n📊 总计生成文件: {len(preprocess_files) + len(feature_files)} 个")

    # 按类型分类显示
    print(f"\n📋 文件类型统计:")
    all_files = preprocess_files + feature_files
    jpg_files = [f for f in all_files if f.lower().endswith(('.jpg', '.jpeg'))]
    png_files = [f for f in all_files if f.lower().endswith('.png')]
    json_files = [f for f in all_files if f.lower().endswith('.json')]

    print(f"   🖼️  图像文件 (.jpg/.jpeg): {len(jpg_files)}")
    print(f"   📊 图表文件 (.png): {len(png_files)}")
    print(f"   📄 数据文件 (.json): {len(json_files)}")

if __name__ == "__main__":
    # 记录开始时间
    start_time = time.time()
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    print_header("可见光图像特征提取系统")
    print(f"🕐 开始时间: {current_time}")

    # 配置参数
    img = r"datasets\image\可见光\vis_00215.jpg"
    output_folder1 = r"Results\可见光\特征提取模块\图像预处理"
    output_folder2 = r"Results\可见光\特征提取模块\特征提取"
    output_folder3 = r"Results\可见光\特征提取模块\识别有效性筛选"

    print(f"📷 输入图像: {img}")
    print(f"📁 预处理输出: {output_folder1}")
    print(f"📁 特征提取输出: {output_folder2}")

    # 检查输入文件
    if not os.path.exists(img):
        print(f"❌ 错误: 输入图像不存在 - {img}")
        exit(1)

    print(f"✅ 输入图像存在，大小: {os.path.getsize(img)} 字节")

    #########调用方法##########
    try:
        print_section("第一步: 图像预处理")
        print("🔄 正在进行图像预处理...")
        ImageProcessor.process_ImageProcessor(img, output_folder1)
        print("✅ 图像预处理完成")

        print_section("第二步: 特征提取")
        print("🔄 正在进行特征提取...")
        results_files = []
        FeatureExtractor.process_FeatureExtractor(output_folder1, output_folder2, results_files)
        print("✅ 特征提取完成")

        # 计算处理时间
        end_time = time.time()
        processing_time = end_time - start_time

        # 打印结果摘要
        print_result_summary(results_files, output_folder1, output_folder2)

        print_section("处理完成")
        print(f"⏱️  总处理时间: {processing_time:.2f} 秒")
        print(f"🎉 所有处理步骤已完成!")

    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


