import requests
import json
import os

def test_multiple_features():
    """测试多图像特征提取功能"""
    
    # 服务器地址
    url = "http://127.0.0.1:6000/extract/multiple"
    
    # 测试图像路径
    image_path = "E:\\z30\\DSC_4776.JPG"
    
    if not os.path.exists(image_path):
        print(f"错误：找不到测试图像 {image_path}")
        return
    
    try:
        print("正在进行多图像特征提取...")
        
        # 准备请求数据
        with open(image_path, 'rb') as f:
            files = {'image': f}
            
            # 发送请求
            response = requests.post(url, files=files)
        
        if response.status_code == 200:
            # 解析JSON响应
            result = response.json()
            print("✓ 多图像特征提取成功！")
            print(f"结果文件夹: {result['folder']}")
            print(f"文件夹路径: {result['folder_path']}")
            print("生成的文件:")
            for file in result['files']:
                print(f"  - {file}")
            
            # 显示完整的响应
            print("\n完整响应:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
        else:
            print(f"✗ 多图像特征提取失败：{response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"✗ 请求出错：{str(e)}")

def check_service():
    """检查服务是否运行"""
    try:
        response = requests.get("http://127.0.0.1:6000")
        return True
    except:
        return False

if __name__ == "__main__":
    print("=== 多图像特征提取测试 ===")
    
    if not check_service():
        print("✗ 服务未启动，请先运行: python app/main.py")
    else:
        print("✓ 服务正在运行")
        test_multiple_features()
