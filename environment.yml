name: zhenghe
channels:
  - defaults
dependencies:
  - bzip2=1.0.8=h2bbff1b_6
  - ca-certificates=2025.7.15=haa95532_0
  - expat=2.7.1=h8ddb27b_0
  - libffi=3.4.4=hd77b12b_1
  - openssl=3.0.17=h35632f6_0
  - pip=25.1=pyhc872135_2
  - python=3.11.13=h981015d_0
  - setuptools=78.1.1=py311haa95532_0
  - sqlite=3.50.2=hda9a48d_1
  - tk=8.6.14=h5e9d12e_1
  - tzdata=2025b=h04d1e81_0
  - ucrt=10.0.22621.0=haa95532_0
  - vc=14.3=h2df5915_10
  - vc14_runtime=14.44.35208=h4927774_10
  - vs2015_runtime=14.44.35208=ha6b5a95_10
  - wheel=0.45.1=py311haa95532_0
  - xz=5.6.4=h4754444_1
  - zlib=1.2.13=h8cc25b3_1
  - pip:
      - certifi==2025.8.3
      - charset-normalizer==3.4.3
      - colorama==0.4.6
      - contourpy==1.3.3
      - cycler==0.12.1
      - filelock==3.18.0
      - fonttools==4.59.0
      - fsspec==2025.7.0
      - idna==3.10
      - imageio==2.37.0
      - importlib-resources==6.5.2
      - jinja2==3.1.6
      - joblib==1.5.1
      - kiwisolver==1.4.9
      - lazy-loader==0.4
      - markupsafe==3.0.2
      - matplotlib==3.10.5
      - mpmath==1.3.0
      - networkx==3.5
      - numpy==2.2.6
      - opencv-python==*********
      - packaging==25.0
      - pillow==11.3.0
      - pyparsing==3.2.3
      - python-dateutil==2.9.0.post0
      - pyyaml==6.0.2
      - requests==2.32.4
      - scikit-image==0.25.2
      - scikit-learn==1.7.1
      - scipy==1.16.1
      - six==1.17.0
      - sympy==1.14.0
      - threadpoolctl==3.6.0
      - tifffile==2025.6.11
      - torch==2.8.0+cu128
      - torchvision==0.23.0+cu128
      - tqdm==4.67.1
      - typing-extensions==4.14.1
      - urllib3==2.5.0
      - zipp==3.23.0

