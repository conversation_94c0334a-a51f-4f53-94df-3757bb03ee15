# from flask import Blueprint, request, jsonify, send_file
# from io import BytesIO
# from PIL import Image
#
# preprocess_bp = Blueprint('preprocess', __name__)
#
# @preprocess_bp.route('/visible_light', methods=['POST'])
# def preprocess_visible_light():
#     """
#     可见光图像预处理接口
#     接收：图像文件（支持jpg、png等格式）
#     返回：预处理后的图像文件
#     """
#     try:
#         # 检查请求中是否包含文件
#         if 'image' not in request.files:
#             return jsonify({
#                 "status": "error",
#                 "message": "请求中缺少图像文件"
#             }), 400
#
#         # 获取上传的图像文件
#         file = request.files['image']
#
#         # 检查文件是否有文件名
#         if file.filename == '':
#             return jsonify({
#                 "status": "error",
#                 "message": "未选择图像文件"
#             }), 400
#
#         # 读取图像
#         img = Image.open(file.stream).convert('RGB')
#
#         #添加实际算法处理!!!
#
#         # 将处理后的图像保存到内存中
#         buffer = BytesIO()
#         img.save(buffer, format='JPEG')
#         buffer.seek(0)
#
#         # 返回处理后的图像文件
#         return send_file(
#             buffer,
#             mimetype='image/jpeg',
#         )
#
#     except Exception as e:
#         return jsonify({
#             "status": "error",
#             "message": str(e)
#         }), 500
#
# #可以按照可见光、红外和SAR分类写三个服务函数


from flask import Blueprint, request, jsonify, send_file
from io import BytesIO
from PIL import Image
import cv2
import numpy as np
from skimage.feature import local_binary_pattern

preprocess_bp = Blueprint('preprocess', __name__)


# ---------------------- 直接内联算法逻辑 ----------------------
def denoise_sharpen_equalize(img_np):
    """去噪、锐化、直方图均衡化（OpenCV BGR 图像）"""
    # 去噪
    denoised = cv2.medianBlur(img_np, 3)
    # 锐化
    kernel = np.array([[0, -1, 0],
                       [-1, 5, -1],
                       [0, -1, 0]])
    sharpened = cv2.filter2D(denoised, -1, kernel)
    # 直方图均衡化（YUV 空间）
    img_yuv = cv2.cvtColor(sharpened, cv2.COLOR_BGR2YUV)
    img_yuv[:, :, 0] = cv2.equalizeHist(img_yuv[:, :, 0])
    equalized = cv2.cvtColor(img_yuv, cv2.COLOR_YUV2BGR)
    return equalized


def extract_harris(img_np_gray):
    """Harris 角点检测（返回可视化图像）"""
    try:
        harris_corners = cv2.cornerHarris(np.float32(img_np_gray), 2, 3, 0.04)
        harris_img_color = cv2.cvtColor(img_np_gray, cv2.COLOR_GRAY2BGR)
        harris_img_color[harris_corners > 0.01 * harris_corners.max()] = [0, 0, 255]
        return harris_img_color
    except Exception as e:
        print(f"Harris 失败: {e}")
        return None


def extract_brisk(img_np_gray):
    """BRISK 特征检测（返回可视化图像）"""
    try:
        brisk = cv2.BRISK_create()
        kp_brisk, _ = brisk.detectAndCompute(img_np_gray, None)
        brisk_img = cv2.drawKeypoints(img_np_gray, kp_brisk, None,
                                      color=(255, 0, 0),
                                      flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS)
        return brisk_img
    except Exception as e:
        print(f"BRISK 失败: {e}")
        return None


def extract_lbp(img_np_gray):
    """LBP 纹理特征（返回可视化图像）"""
    try:
        radius = 1
        n_points = 8 * radius
        lbp = local_binary_pattern(img_np_gray, n_points, radius, method='uniform')
        lbp_img = (lbp / lbp.max() * 255).astype(np.uint8)
        return cv2.cvtColor(lbp_img, cv2.COLOR_GRAY2BGR)
    except Exception as e:
        print(f"LBP 失败: {e}")
        return None


# ---------------------- 算法逻辑结束 ----------------------


@preprocess_bp.route('/visible_light', methods=['POST'])
def preprocess_visible_light():
    """
    可见光图像预处理接口（内联算法）
    功能：
    1. 上传单张图像 → 2. 去噪/锐化/均衡化 → 3. 提取 Harris/BRISK/LBP 特征
    返回：预处理后图像（可扩展返回特征图，按需调整）
    """
    try:
        # 1. 接收上传的图像文件
        if 'image' not in request.files:
            return jsonify({"status": "error", "message": "缺少图像文件"}), 400
        file = request.files['image']
        if not file.filename:
            return jsonify({"status": "error", "message": "未选择文件"}), 400

        # 2. PIL Image 转 OpenCV（BGR 格式）
        img_pil = Image.open(file.stream).convert('RGB')
        img_np = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)

        # 3. 执行预处理（去噪、锐化、均衡化）
        processed_np = denoise_sharpen_equalize(img_np)

        # 4. 提取特征（基于灰度图）
        img_gray = cv2.cvtColor(processed_np, cv2.COLOR_BGR2GRAY)
        harris_vis = extract_harris(img_gray)
        brisk_vis = extract_brisk(img_gray)
        lbp_vis = extract_lbp(img_gray)

        # （可选）保存特征图到本地或返回，这里仅演示主预处理图返回

        # 5. 预处理图转 BytesIO（用于 send_file）
        processed_pil = Image.fromarray(cv2.cvtColor(processed_np, cv2.COLOR_BGR2RGB))
        buffer = BytesIO()
        processed_pil.save(buffer, format='JPEG')
        buffer.seek(0)

        # 6. 返回预处理后的图像
        return send_file(
            buffer,
            mimetype='image/jpeg',
            as_attachment=False,
            download_name='preprocessed.jpg'
        )

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500