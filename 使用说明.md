# 特征提取接口使用说明

## 接口信息

### 端点
```
POST /api/extract_features/multiple
```

### 功能
完整的图像特征提取服务，包括：
- 图像预处理（去噪、锐化、直方图均衡化）
- 多种特征提取（颜色直方图、GLCM纹理、Hu矩、HOG、YOLO检测等）

## 使用方法

### 1. 启动服务器
```bash
python test_server.py
```
服务器将在 `http://localhost:5000` 启动

### 2. 发送请求

#### 使用curl
```bash
curl -X POST -F "image=@path/to/your/image.jpg" http://localhost:5000/api/extract_features/multiple
```

#### 使用Python requests
```python
import requests

url = "http://localhost:5000/api/extract_features/multiple"
with open("your_image.jpg", "rb") as f:
    files = {"image": f}
    response = requests.post(url, files=files)
    result = response.json()
    print(result)
```

### 3. 运行测试
```bash
python test_extract_features_api.py
```

## 请求参数

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| image  | file | 是   | 图像文件 (支持 jpg, jpeg, png, bmp, tif, tiff) |

## 响应格式

### 成功响应 (200)
```json
{
    "status": "success",
    "message": "处理完成",
    "original_filename": "原始文件名.jpg",
    "preprocessed_folder": "预处理结果文件夹路径",
    "features_folder": "特征提取结果文件夹路径",
    "generated_files": [
        {
            "type": "preprocessed|features",
            "filename": "生成的文件名",
            "path": "文件完整路径"
        }
    ],
    "total_files": 生成文件总数,
    "processing_id": "处理唯一标识"
}
```

### 错误响应 (400/500)
```json
{
    "status": "error",
    "message": "错误描述信息"
}
```

## 生成的文件

### 预处理结果
- `*_denoised.jpg` - 去噪后的图像
- `*_sharpened.jpg` - 锐化后的图像
- `*_equalized.jpg` - 直方图均衡化后的图像

### 特征提取结果
- `*_color_hist.png` - 颜色直方图可视化
- `*_glcm_features.png` - GLCM纹理特征可视化
- `*_hu_moments.json` - Hu矩不变量特征数据
- `*_hog_features.png` - HOG特征可视化
- `*_object_quaternions.json` - 目标四元数特征
- `*_moment_quaternions.json` - 矩四元数特征
- `*_yolo_detections.json` - YOLO目标检测结果

## 注意事项

1. **处理时间**: 完整的特征提取可能需要较长时间，建议设置足够的超时时间
2. **文件大小**: 大图像会增加处理时间，建议适当调整图像尺寸
3. **存储空间**: 生成的文件会保存在 `Results/可见光/特征提取模块/` 目录下
4. **并发处理**: 每次请求会创建唯一的文件夹，支持并发处理

## 故障排除

### 常见问题

1. **连接失败**
   - 确保服务器正在运行
   - 检查端口5000是否被占用

2. **请求超时**
   - 增加客户端超时时间
   - 尝试使用更小的图像

3. **处理失败**
   - 检查图像格式是否支持
   - 查看服务器控制台的错误日志
   - 确保所有依赖包已正确安装

### 依赖检查
确保安装了以下Python包：
```bash
pip install opencv-python numpy pillow flask requests matplotlib scikit-image ultralytics torch
```

## 示例代码

完整的Python客户端示例：

```python
import requests
import json

def extract_features(image_path):
    url = "http://localhost:5000/api/extract_features/multiple"
    
    try:
        with open(image_path, 'rb') as f:
            files = {'image': (image_path, f, 'image/jpeg')}
            response = requests.post(url, files=files, timeout=300)
            
        if response.status_code == 200:
            result = response.json()
            print(f"处理成功: {result['message']}")
            print(f"生成文件数: {result['total_files']}")
            return result
        else:
            print(f"处理失败: {response.json()['message']}")
            return None
            
    except Exception as e:
        print(f"请求失败: {e}")
        return None

# 使用示例
if __name__ == "__main__":
    result = extract_features("your_image.jpg")
    if result:
        print("特征提取完成！")
```
