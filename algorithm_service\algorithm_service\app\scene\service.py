import os
import logging

logger = logging.getLogger(__name__)

class SceneService:
    @staticmethod
    def get_strategy_by_scene(scene_type):
        """
        根据场景类型获取对应的策略
        :param scene_type: 场景类型（如 "day" "night" "rainy"）
        :return: 策略字典 + HTTP 状态码
        """
        # 模拟场景与策略的映射（实际应从数据库/配置文件读取）
        scene_strategies = {
            "day": {"name": "白天策略", "priority": "high", "actions": ["抓拍", "高清传输"]},
            "night": {"name": "夜间策略", "priority": "medium", "actions": ["红外补光", "低码率传输"]},
            "rainy": {"name": "雨天策略", "priority": "high", "actions": ["去雾处理", "增强对比度"]},
        }

        # 业务逻辑：校验场景类型、生成策略
        if scene_type not in scene_strategies:
            return {"status": "error", "message": f"不支持的场景类型: {scene_type}"}, 400

        strategy = scene_strategies[scene_type]
        return {"status": "success", "data": strategy}, 200

    @staticmethod
    def create_scene_config(scene_data):
        """
        创建场景配置（示例：接收场景数据，模拟保存逻辑）
        :param scene_data: 场景配置数据（字典）
        :return: 结果信息 + HTTP 状态码
        """
        # 业务逻辑：校验数据、模拟保存（实际可写数据库/文件）
        required_keys = ["name", "threshold"]
        for key in required_keys:
            if key not in scene_data:
                return {"status": "error", "message": f"缺少必要字段: {key}"}, 400

        # 模拟保存成功
        return {"status": "success", "message": f"场景 {scene_data['name']} 配置创建成功"}, 201