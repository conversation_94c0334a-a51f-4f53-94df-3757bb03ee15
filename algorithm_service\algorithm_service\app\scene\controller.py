from flask import Blueprint, request, jsonify
from app.scene.service import SceneService

scene_bp = Blueprint('scene', __name__)

@scene_bp.route('/strategy', methods=['GET'])
def get_scene_strategy():
    """
    接口：根据场景类型获取策略
    请求参数：?scene_type=day
    响应：返回对应场景的策略
    """
    # 1. 解析请求参数（Controller 层逻辑）
    scene_type = request.args.get('scene_type')
    if not scene_type:
        return jsonify({"status": "error", "message": "缺少 scene_type 参数"}), 400

    # 2. 调用 Service 处理业务逻辑（无任何业务逻辑，仅转发）
    result, status_code = SceneService.get_strategy_by_scene(scene_type)

    # 3. 返回响应（Controller 层逻辑）
    return jsonify(result), status_code

@scene_bp.route('/config', methods=['POST'])
def create_scene_config():
    """
    接口：创建场景配置
    请求体：JSON 格式的场景配置数据
    响应：返回创建结果
    """
    # 1. 解析请求体（Controller 层逻辑）
    try:
        scene_data = request.json
    except Exception:
        return jsonify({"status": "error", "message": "请求体不是合法的 JSON"}), 400

    if not scene_data:
        return jsonify({"status": "error", "message": "请求体不能为空"}), 400

    # 2. 调用 Service 处理业务逻辑（无任何业务逻辑，仅转发）
    result, status_code = SceneService.create_scene_config(scene_data)

    # 3. 返回响应（Controller 层逻辑）
    return jsonify(result), status_code