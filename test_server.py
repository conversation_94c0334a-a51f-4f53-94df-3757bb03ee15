#!/usr/bin/env python3
"""
简化的Flask服务器用于测试特征提取接口
"""

from flask import Flask
import sys
import os

# 添加项目路径到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入接口蓝图
from algorithm_service.algorithm_service.app.api.extract_features import extract_features_bp

app = Flask(__name__)

# 注册蓝图
app.register_blueprint(extract_features_bp, url_prefix='/api/extract_features')

@app.route('/')
def index():
    return {
        "message": "特征提取API服务器",
        "endpoints": [
            "/api/extract_features/visible_light",
            "/api/extract_features/infrared",
            "/api/extract_features/sar",
            "/api/extract_features/multiple"
        ]
    }

@app.route('/health')
def health():
    return {"status": "healthy"}

if __name__ == '__main__':
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
