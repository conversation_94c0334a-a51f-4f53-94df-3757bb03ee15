from model.目标识别.可见光.detect_vi import CustomYOLOv8Detector
import cv2
import os

def main(image_path):
    # 1. 创建检测器实例
    # 请将 'your_model_path.pt' 替换为您实际的模型路径
    detector = CustomYOLOv8Detector(
        model_path=r"model\目标识别\可见光\model\yolo11n.pt",  # 替换为您的模型路径
        conf_threshold=0.5,  # 置信度阈值
        iou_threshold=0.4    # IOU阈值
    )
    

    if os.path.exists(image_path):
        try:
            # 执行检测
            results, output_image = detector.detect_image(image_path)
            
            # 获取检测到的对象信息
            objects = detector.get_detected_objects(results)
            
            print(f"在 {image_path} 中检测到 {len(objects)} 个目标:")
            for i, obj in enumerate(objects):
                print(f"  {i+1}. {obj['class_name']} - 置信度: {obj['confidence']:.2f} "
                      f"- 位置: [{obj['bbox'][0]}, {obj['bbox'][1]}, {obj['bbox'][2]}, {obj['bbox'][3]}]")
            
            # 保存结果图像
            output_path = "result_" + os.path.basename(image_path)
            cv2.imwrite(output_path, output_image)
            print(f"结果图像已保存到: {output_path}")
            
        except Exception as e:
            print(f"检测图像时出错: {e}")
    else:
        print(f"图像文件不存在: {image_path}")

    # # 3. 调整参数重新检测
    # print("\n调整参数后重新检测...")
    # detector.set_confidence_threshold(0.7)  # 提高置信度阈值
    # detector.set_iou_threshold(0.3)         # 调整IOU阈值
    
    # if os.path.exists(image_path):
    #     try:
    #         results, output_image = detector.detect_image(image_path)
    #         objects = detector.get_detected_objects(results)
            
    #         print(f"调整参数后检测到 {len(objects)} 个目标:")
    #         for i, obj in enumerate(objects):
    #             print(f"  {i+1}. {obj['class_name']} - 置信度: {obj['confidence']:.2f}")
            
    #         # 保存调整参数后的结果
    #         output_path2 = "result_high_conf_" + os.path.basename(image_path)
    #         cv2.imwrite(output_path2, output_image)
    #         print(f"高置信度结果图像已保存到: {output_path2}")
            
    #     except Exception as e:
    #         print(f"重新检测时出错: {e}")
    
    # # 4. 处理视频帧示例
    # print("\n视频处理示例...")
    # 从摄像头读取帧或加载视频帧
    # cap = cv2.VideoCapture(0)  # 使用摄像头
    # ret, frame = cap.read()
    # if ret:
    #     results, output_frame = detector.detect_frame(frame)
    #     objects = detector.get_detected_objects(results)
    #     print(f"视频帧中检测到 {len(objects)} 个目标")
    #     cv2.imshow('Detection Result', output_frame)
    #     cv2.waitKey(0)
    #     cv2.destroyAllWindows()
    # cap.release()

if __name__ == "__main__":
    # 2. 检测单张图像
    image_path = r"E:\融合数据集\IVdata\visible\VI08_000913.jpg"  # 替换为您的图像路径
    main(image_path)