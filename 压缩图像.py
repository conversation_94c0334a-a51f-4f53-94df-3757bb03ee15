#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像压缩脚本
功能：将指定路径下大于10MB的图像压缩到9MB以下，保持画质
"""

import os
import sys
from PIL import Image
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_image_files(path):
    """
    获取路径下的所有图像文件
    
    Args:
        path (str): 要搜索的路径
        
    Returns:
        list: 图像文件路径列表
    """
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    image_files = []
    
    for root, _, files in os.walk(path):
        for file in files:
            if os.path.splitext(file.lower())[1] in image_extensions:
                image_files.append(os.path.join(root, file))
    
    return image_files

def get_file_size(filepath):
    """
    获取文件大小（以MB为单位）
    
    Args:
        filepath (str): 文件路径
        
    Returns:
        float: 文件大小（MB）
    """
    return os.path.getsize(filepath) / (1024 * 1024)

def compress_image(filepath, target_size_mb=9, min_quality=30):
    """
    压缩图像到目标大小
    
    Args:
        filepath (str): 图像文件路径
        target_size_mb (float): 目标大小（MB）
        min_quality (int): 最小质量值
        
    Returns:
        bool: 是否成功压缩
    """
    try:
        # 打开原始图像
        with Image.open(filepath) as img:
            # 保存原始格式
            original_format = img.format
            
            # 如果是RGBA模式且目标格式是JPEG，转换为RGB
            if img.mode == 'RGBA' and original_format == 'JPEG':
                img = img.convert('RGB')
            elif img.mode not in ('RGB', 'RGBA') and original_format in ('JPEG', 'PNG'):
                img = img.convert('RGB')
            
            # 获取原始文件大小
            original_size = get_file_size(filepath)
            logger.info(f"处理文件: {filepath}, 原始大小: {original_size:.2f}MB")
            
            if original_size <= target_size_mb:
                logger.info(f"文件 {filepath} 已小于等于 {target_size_mb}MB，无需压缩")
                return True
            
            # 使用二分法查找最佳质量参数
            low_quality, high_quality = min_quality, 95
            best_quality = high_quality
            temp_file = filepath + '.temp'
            
            while low_quality <= high_quality:
                mid_quality = (low_quality + high_quality) // 2
                
                # 保存临时文件
                if original_format == 'JPEG':
                    img.save(temp_file, format=original_format, quality=mid_quality, optimize=True)
                elif original_format == 'PNG':
                    img.save(temp_file, format=original_format, compress_level=min(mid_quality // 10, 9), optimize=True)
                else:
                    img.save(temp_file, format=original_format, quality=mid_quality, optimize=True)
                
                temp_size = get_file_size(temp_file)
                
                if temp_size <= target_size_mb:
                    best_quality = mid_quality
                    low_quality = mid_quality + 1
                else:
                    high_quality = mid_quality - 1
            
            # 使用找到的最佳质量保存文件
            if original_format == 'JPEG':
                img.save(filepath, format=original_format, quality=best_quality, optimize=True)
            elif original_format == 'PNG':
                img.save(filepath, format=original_format, compress_level=min(best_quality // 10, 9), optimize=True)
            else:
                img.save(filepath, format=original_format, quality=best_quality, optimize=True)
            
            final_size = get_file_size(filepath)
            logger.info(f"压缩完成: {filepath}, 最终大小: {final_size:.2f}MB, 质量参数: {best_quality}")
            
            # 清理临时文件
            if os.path.exists(temp_file):
                os.remove(temp_file)
            
            return True
            
    except Exception as e:
        logger.error(f"处理文件 {filepath} 时出错: {str(e)}")
        return False

def process_images_in_path(path, size_threshold=10):
    """
    处理路径下的所有图像文件
    
    Args:
        path (str): 要处理的路径
        size_threshold (float): 文件大小阈值（MB）
    """
    if not os.path.exists(path):
        logger.error(f"路径 {path} 不存在")
        return
    
    # 获取所有图像文件
    image_files = get_image_files(path)
    logger.info(f"找到 {len(image_files)} 个图像文件")
    
    processed_count = 0
    failed_count = 0
    
    for filepath in image_files:
        try:
            file_size = get_file_size(filepath)
            
            # 只处理大于阈值的文件
            if file_size > size_threshold:
                logger.info(f"开始处理: {filepath} ({file_size:.2f}MB)")
                if compress_image(filepath):
                    processed_count += 1
                else:
                    failed_count += 1
            else:
                logger.info(f"跳过文件: {filepath} ({file_size:.2f}MB)")
                
        except Exception as e:
            logger.error(f"处理文件 {filepath} 时出错: {str(e)}")
            failed_count += 1
    
    logger.info(f"处理完成: 成功处理 {processed_count} 个文件，失败 {failed_count} 个文件")

def main():
    """
    主函数
    """
    print("图像压缩工具")
    print("功能：将指定路径下大于10MB的图像压缩到9MB以下")
    print("-" * 50)
    
    # 获取用户输入的路径
    while True:
        path = input("请输入图像文件夹路径: ").strip()
        
        # 处理引号包围的路径
        if path.startswith('"') and path.endswith('"'):
            path = path[1:-1]
        elif path.startswith("'") and path.endswith("'"):
            path = path[1:-1]
        
        if os.path.exists(path):
            break
        else:
            print(f"路径 '{path}' 不存在，请重新输入！")
    
    print(f"开始处理路径: {path}")
    process_images_in_path(path)

if __name__ == "__main__":
    main()