from flask import Blueprint, request, jsonify

strategy_recognition_bp = Blueprint('strategy_recognition', __name__)

@strategy_recognition_bp.route('/recognize_scenario', methods=['POST'])
def recognize_scenario():
    """
    对应 StrategyRecognitionService.recognize_scenario
    功能：识别图像所属场景（如军事、交通）
    请求体：{"image_data": "base64编码"}
    返回：场景标签（JSON格式）
    """
    try:
        data = request.get_json()
        # 示例：简单判断（实际用模型分类）
        return jsonify({
            "status": "success",
            "scenario": "Military"  # 占位，实际根据图像判断
        })
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@strategy_recognition_bp.route('/recognize_target', methods=['POST'])
def recognize_target():
    """
    对应 StrategyRecognitionService.recognize_target
    功能：识别场景中的目标（如坦克、车辆）
    请求体：{"scenario": "Military", "image_data": "..."}
    返回：目标列表（JSON格式）
    """
    try:
        data = request.get_json()
        scenario = data.get('scenario')
        # 示例：根据场景返回模拟目标
        if scenario == "Military":
            return jsonify({
                "status": "success",
                "targets": [{"name": "Tank", "position": [100, 200]}]
            })
        return jsonify({"status": "error", "message": "Unsupported scenario"}), 400
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@strategy_recognition_bp.route('/recognize_intent', methods=['POST'])
def recognize_intent():
    """
    对应 StrategyRecognitionService.recognize_intent
    功能：识别目标意图（如移动、静止）
    请求体：{"targets": [...]}（上一步输出）
    返回：意图标签（JSON格式）
    """
    try:
        data = request.get_json()
        targets = data.get('targets')
        # 示例：根据目标数量判断意图
        return jsonify({
            "status": "success",
            "intent": "Moving" if len(targets) > 0 else "Stationary"
        })
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500