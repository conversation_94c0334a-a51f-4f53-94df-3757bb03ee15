import cv2
import numpy as np
import os
from typing import Union, List
from skimage.feature import local_binary_pattern
import torch
import matplotlib.pyplot as plt
from PIL import Image
from ultralytics import YOLO

class ImageProcessor:
    def __init__(self, output_dir):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

    def process_image(self, img_path):
        print(f"处理图像: {img_path}")
        img = cv2.imread(img_path)
        if img is None:
            print(f"未找到图像: {img_path}")
            return
        # 去噪
        denoised = cv2.medianBlur(img, 3)
        # 锐化
        kernel = np.array([[0, -1, 0],
                           [-1, 5, -1],
                           [0, -1, 0]])
        sharpened = cv2.filter2D(denoised, -1, kernel)
        # 直方图均衡化
        img_yuv = cv2.cvtColor(sharpened, cv2.COLOR_BGR2YUV)
        img_yuv[:, :, 0] = cv2.equalizeHist(img_yuv[:, :, 0])
        equalized = cv2.cvtColor(img_yuv, cv2.COLOR_YUV2BGR)
        # 保存
        base_name = os.path.splitext(os.path.basename(img_path))[0]
        print(f"baocundizhi{self.output_dir}")
        cv2.imwrite(os.path.join(self.output_dir, f"{base_name}_denoised.jpg"), denoised)
        cv2.imwrite(os.path.join(self.output_dir,f"{base_name}_sharpened.jpg"), sharpened)
        cv2.imwrite(os.path.join(self.output_dir,f"{base_name}_equalized.jpg"), equalized)

    @staticmethod
    def process_ImageProcessor(inputs: Union[str, List[str]], output_dir: str):
        processor = ImageProcessor(output_dir)
        if isinstance(inputs, str):
            if os.path.isfile(inputs):
                print(f"zheliu{inputs} ")
                processor.process_image(inputs)
            elif os.path.isdir(inputs):
                for file in os.listdir(inputs):
                    if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')):
                        img_path = os.path.join(inputs, file)
                        processor.process_image(img_path)
            else:
                print("输入路径既不是文件也不是文件夹！")
        elif isinstance(inputs, list):
            for img_path in inputs:
                processor.process_image(img_path)
        else:
            print("输入类型不支持！")



class FeatureExtractor:
    def __init__(self, output_dir):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self.yolo_model=r'model\特征提取\可见光\yolo11n.pt'



    def extract_moment_quaternions(self, img_path):
        """
        基于图像矩计算四元数特征
        """
        try:
            # 读取图像
            img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
            if img is None:
                print(f"未找到图像: {img_path}")
                return None
                
            # 使用YOLO检测目标
            model = YOLO(self.yolo_model)
            color_img = cv2.imread(img_path)
            results = model(color_img)
            
            quaternion_features = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for i, box in enumerate(boxes):
                        # 获取边界框
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        
                        # 提取目标区域
                        x1_int, y1_int = max(0, int(x1)), max(0, int(y1))
                        x2_int, y2_int = min(img.shape[1], int(x2)), min(img.shape[0], int(y2))
                        
                        target_region = img[y1_int:y2_int, x1_int:x2_int]
                        
                        if target_region.size > 0:
                            # 计算图像矩
                            moments = cv2.moments(target_region)
                            
                            # 计算中心矩
                            if moments['m00'] != 0:
                                # 归一化中心矩
                                nu20 = moments['mu20'] / moments['m00']**2
                                nu02 = moments['mu02'] / moments['m00']**2
                                nu11 = moments['mu11'] / moments['m00']**2
                                nu30 = moments['mu30'] / moments['m00']**2.5
                                
                                # 构造四元数特征
                                quaternion = {
                                    'w': float(moments['m00']),  # 零阶矩（面积）
                                    'x': float(nu20),            # 归一化二阶中心矩
                                    'y': float(nu02),            # 归一化二阶中心矩
                                    'z': float(nu11)             # 归一化二阶中心矩
                                }
                                
                                quaternion_features.append({
                                    'object_id': i,
                                    'quaternion': quaternion,
                                    'bounding_box': [float(x1), float(y1), float(x2), float(y2)]
                                })
                                
                                print(f"目标 {i} 的四元数矩特征: {quaternion}")
            
            # 保存结果
            if quaternion_features:
                import json
                base_name = os.path.splitext(os.path.basename(img_path))[0]
                moment_quat_path = os.path.join(self.output_dir, f"{base_name}_moment_quaternions.json")
                
                output_data = {
                    'image_path': img_path,
                    'features': quaternion_features
                }
                
                with open(moment_quat_path, 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, ensure_ascii=False, indent=2)
                print(f"矩四元数特征已保存至: {moment_quat_path}")
                
            return quaternion_features
            
        except Exception as e:
            print(f"矩四元数特征提取失败: {e}")
            return None


    def detect_objects_and_calculate_ratios(self, img_path):
        """
        使用YOLO检测图像中的目标并计算每个目标的长宽比
        
        Args:
            img_path (str): 图像路径
            
        Returns:
            list: 包含每个检测到的目标信息的列表
        """
        print(f"使用YOLO检测目标: {img_path}")
        
        try:
            # 加载YOLO模型
            model = YOLO(self.yolo_model)
            
            # 读取彩色图像用于YOLO检测
            yolo_img = cv2.imread(img_path)
            if yolo_img is None:
                print(f"YOLO: 未找到图像: {img_path}")
                return []
                
            # 使用YOLO进行目标检测
            results = model(yolo_img)
            
            base_name = os.path.splitext(os.path.basename(img_path))[0]
            objects_info = []
            
            # 遍历检测结果
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for i, box in enumerate(boxes):
                        # 获取边界框坐标 (xyxy格式: x1, y1, x2, y2)
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        width = x2 - x1
                        height = y2 - y1
                        
                        # 计算长宽比 (较长边/较短边)
                        aspect_ratio = max(width, height) / min(width, height) if min(width, height) > 0 else 0
                        
                        # 获取类别信息和置信度
                        class_id = int(box.cls[0].cpu().numpy()) if box.cls is not None else -1
                        confidence = float(box.conf[0].cpu().numpy()) if box.conf is not None else 0.0
                        
                        # 保存目标信息
                        obj_info = {
                            'id': i,
                            'bbox': [float(x1), float(y1), float(x2), float(y2)],
                            'width': float(width),
                            'height': float(height),
                            'aspect_ratio': float(aspect_ratio),
                            'class_id': class_id,
                            'confidence': confidence
                        }
                        objects_info.append(obj_info)
                        
                        # 在图像上绘制边界框和长宽比信息
                        cv2.rectangle(yolo_img, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
                        cv2.putText(yolo_img, f'ID:{i} AR:{aspect_ratio:.2f}', (int(x1), int(y1)-10), 
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                        
                        print(f"目标 {i}: 类别={class_id}, 置信度={confidence:.2f}, "
                            f"宽={width:.2f}, 高={height:.2f}, 长宽比={aspect_ratio:.2f}")
            
            # 保存带标注的图像
            if objects_info:
                annotated_img_path = os.path.join(self.output_dir, f"{base_name}_yolo_aspect_ratio.jpg")
                cv2.imwrite(annotated_img_path, yolo_img)
                print(f"检测到 {len(objects_info)} 个目标，标注图像保存至: {annotated_img_path}")
                
                # 保存目标、类别和长宽比信息到JSON文件
                import json
                ratio_data_path = os.path.join(self.output_dir, f"{base_name}_aspect_ratios.json")
                
                # 记录目标、类别和对应的长宽比
                output_data = {
                    'image_path': img_path,
                    'object_count': len(objects_info),
                    'objects': []
                }
                
                for obj in objects_info:
                    object_data = {
                        'object_id': obj['id'],
                        'class_id': obj['class_id'],
                        'aspect_ratio': obj['aspect_ratio']
                    }
                    output_data['objects'].append(object_data)
                
                with open(ratio_data_path, 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, ensure_ascii=False, indent=2)
                print(f"目标类别和长宽比数据已保存至: {ratio_data_path}")
                
                return objects_info
            else:
                print("未检测到任何目标")
                
                # 即使没有检测到目标也创建一个空的记录文件
                import json
                ratio_data_path = os.path.join(self.output_dir, f"{base_name}_aspect_ratios.json")
                output_data = {
                    'image_path': img_path,
                    'object_count': 0,
                    'objects': []
                }
                with open(ratio_data_path, 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, ensure_ascii=False, indent=2)
                print(f"空的目标类别和长宽比数据已保存至: {ratio_data_path}")
                return []
                
        except Exception as e:
            print(f"YOLO检测或长宽比计算失败: {e}")
            return []


    def extract_harris_corner_gaussian_distribution(self, img_path):
        """
        基于Harris角点检测提取角点的高斯分布特征
        """
        try:
            # 读取图像
            img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
            if img is None:
                print(f"未找到图像: {img_path}")
                return None
                
            base_name = os.path.splitext(os.path.basename(img_path))[0]
            
            # 使用您现有的Harris角点检测代码
            harris_img = img.copy()
            harris_corners = cv2.cornerHarris(np.float32(harris_img), 2, 3, 0.04)
            
            # 获取角点坐标
            threshold = 0.01 * harris_corners.max()
            corner_points = np.where(harris_corners > threshold)
            
            if len(corner_points[0]) > 0:
                # 提取角点的x和y坐标
                y_coords = corner_points[0]  # 行坐标
                x_coords = corner_points[1]  # 列坐标
                
                # 计算角点坐标的统计特征
                mean_x = np.mean(x_coords)
                mean_y = np.mean(y_coords)
                std_x = np.std(x_coords)
                std_y = np.std(y_coords)
                
                # 计算协方差矩阵
                coords = np.vstack((x_coords, y_coords))
                cov_matrix = np.cov(coords)
                
                # 计算角点密度
                corner_density = len(x_coords) / (img.shape[0] * img.shape[1])
                
                # 构建高斯分布特征
                gaussian_features = {
                    '角点均值': [float(mean_x), float(mean_y)],
                    '角点方差': [float(std_x), float(std_y)],
                    'covariance_matrix': cov_matrix.tolist(),
                    'corner_count': int(len(x_coords)),
                    'corner_density': float(corner_density),
                    'image_size': [int(img.shape[1]), int(img.shape[0])]  # [width, height]
                }
                
                # 保存高斯分布特征到JSON文件
                gaussian_data_path = os.path.join(self.output_dir, f"{base_name}_高斯分布_角点均值和方差.json")
                
                import json
                output_data = {
                    'image_path': img_path,
                    'gaussian_distribution': gaussian_features,
                    'corner_coordinates': [[float(x), float(y)] for x, y in zip(x_coords, y_coords)]
                }
                
                with open(gaussian_data_path, 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, ensure_ascii=False, indent=2)
                print(f"Harris角点高斯分布特征已保存至: {gaussian_data_path}")
                print(f"Harris角点高斯分布特征: \n {gaussian_features}")

                
                # 可视化角点分布和高斯特征
                color_img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
                
                # 绘制所有角点
                for y, x in zip(y_coords, x_coords):
                    cv2.circle(color_img, (int(x), int(y)), 3, (0, 255, 0), -1)
                
                # 绘制高斯分布中心点
                center_x, center_y = int(mean_x), int(mean_y)
                cv2.circle(color_img, (center_x, center_y), 5, (0, 0, 255), -1)
                
                # 在图像上添加文本信息
                cv2.putText(color_img, f'Corners: {len(x_coords)}', (10, 30), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                cv2.putText(color_img, f'Center: ({center_x}, {center_y})', (10, 60), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                cv2.putText(color_img, f'Std: ({std_x:.1f}, {std_y:.1f})', (10, 90), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                
                # 保存可视化结果
                visualization_path = os.path.join(self.output_dir, f"{base_name}_harris_gaussian_visualization.jpg")
                cv2.imwrite(visualization_path, color_img)
                print(f"Harris角点高斯分布可视化已保存至: {visualization_path}")
                
                return gaussian_features
            else:
                print("Harris角点检测未发现角点")
                # 保存空结果
                import json
                gaussian_data_path = os.path.join(self.output_dir, f"{base_name}_harris_gaussian.json")
                empty_data = {
                    'image_path': img_path,
                    'gaussian_distribution': None,
                    'corner_coordinates': []
                }
                with open(gaussian_data_path, 'w', encoding='utf-8') as f:
                    json.dump(empty_data, f, ensure_ascii=False, indent=2)
                print(f"空的Harris角点高斯分布数据已保存至: {gaussian_data_path}")
                return None
                
        except Exception as e:
            print(f"Harris角点高斯分布提取失败: {e}")
            return None


    def extract_and_save(self, img_path,results_files):
        print(f"处理图像: {img_path}")
        img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
        print("img_path",img_path)
        if img is None:
            print(f"未找到图像: {img_path}")
            return
        base_name = os.path.splitext(os.path.basename(img_path))[0]
        all_path=results_files


       # 添加颜色特征提取（如果是彩色图像）
        color_img = cv2.imread(img_path)
        if color_img is not None:
            # 颜色直方图
            hist_b = cv2.calcHist([color_img], [0], None, [256], [0, 256])
            hist_g = cv2.calcHist([color_img], [1], None, [256], [0, 256])
            hist_r = cv2.calcHist([color_img], [2], None, [256], [0, 256])
            
            # 归一化直方图
            hist_b = hist_b / hist_b.max()
            hist_g = hist_g / hist_g.max()
            hist_r = hist_r / hist_r.max()
            
            # 绘制颜色直方图
            plt.figure(figsize=(10, 4))
            plt.subplot(131)
            plt.plot(hist_b, color='b')
            plt.title('Blue Channel Histogram')
            plt.subplot(132)
            plt.plot(hist_g, color='g')
            plt.title('Green Channel Histogram')
            plt.subplot(133)
            plt.plot(hist_r, color='r')
            plt.title('Red Channel Histogram')
            plt.tight_layout()
            color_hist_path = os.path.join(self.output_dir, f"{base_name}_color_hist.png")
            plt.savefig(color_hist_path)
            plt.close()
            all_path.append(color_hist_path)
            print(f"颜色直方图特征提取成功: {base_name}_color_hist.png")

        # GLCM纹理特征提取
        if img is not None:
            # 计算灰度共生矩阵特征
            from skimage.feature import graycomatrix, graycoprops
            
            # 确保图像是二维的
            if len(img.shape) == 3:
                if img.shape[2] == 1:
                    img = img.squeeze()
                else:
                    img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            elif len(img.shape) != 2:
                print(f"图像维度不正确: {img.shape}")
                return all_path
                
            # 将图像转换为0-31级灰度
            img_32 = np.uint8(img / 8)
            
            # 确保图像数据在有效范围内
            img_32 = np.clip(img_32, 0, 31)
            
            try:
                # 计算GLCM
                glcm = graycomatrix(img_32, [1], [0, 45, 90, 135], levels=32)
                
                # 计算特征
                contrast = graycoprops(glcm, 'contrast').flatten()
                energy = graycoprops(glcm, 'energy').flatten()
                homogeneity = graycoprops(glcm, 'homogeneity').flatten()
                correlation = graycoprops(glcm, 'correlation').flatten()
                
                # 保存GLCM特征可视化
                fig, axes = plt.subplots(2, 2, figsize=(10, 10))
                axes[0, 0].bar([0, 1, 2, 3], contrast)
                axes[0, 0].set_title('Contrast')
                axes[0, 0].set_xticks([0, 1, 2, 3])
                axes[0, 0].set_xticklabels(['0°', '45°', '90°', '135°'])
                
                axes[0, 1].bar([0, 1, 2, 3], energy)
                axes[0, 1].set_title('Energy')
                axes[0, 1].set_xticks([0, 1, 2, 3])
                axes[0, 1].set_xticklabels(['0°', '45°', '90°', '135°'])
                
                axes[1, 0].bar([0, 1, 2, 3], homogeneity)
                axes[1, 0].set_title('Homogeneity')
                axes[1, 0].set_xticks([0, 1, 2, 3])
                axes[1, 0].set_xticklabels(['0°', '45°', '90°', '135°'])
                
                axes[1, 1].bar([0, 1, 2, 3], correlation)
                axes[1, 1].set_title('Correlation')
                axes[1, 1].set_xticks([0, 1, 2, 3])
                axes[1, 1].set_xticklabels(['0°', '45°', '90°', '135°'])
                
                plt.tight_layout()
                glcm_path = os.path.join(self.output_dir, f"{base_name}_glcm_features.png")
                plt.savefig(glcm_path)
                plt.close()
                all_path.append(glcm_path)
                print(f"GLCM纹理特征提取成功: {base_name}_glcm_features.png")
            except Exception as e:
                print(f"GLCM纹理特征提取失败: {e}")

        # Hu矩特征
        if img is not None:
            # 确保图像是二维的
            if len(img.shape) == 3:
                if img.shape[2] == 1:
                    img = img.squeeze()
                else:
                    img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            elif len(img.shape) != 2:
                print(f"图像维度不正确: {img.shape}")
                return all_path
                
            try:
                # 计算图像的Hu矩不变量
                moments = cv2.moments(img)
                hu_moments = cv2.HuMoments(moments).flatten()
                
                # 由于Hu矩值范围很大，通常取对数
                hu_moments_log = []
                for i in range(len(hu_moments)):
                    if hu_moments[i] != 0:
                        hu_moments_log.append(-1 * np.sign(hu_moments[i]) * np.log10(abs(hu_moments[i])))
                    else:
                        hu_moments_log.append(0)
                
                # 可视化Hu矩
                plt.figure(figsize=(10, 4))
                plt.bar(range(1, 8), hu_moments_log)
                plt.xlabel('Hu Moment Index')
                plt.ylabel('Log Value')
                plt.title('Hu Moments (Log Scale)')
                plt.grid(True)
                hu_path = os.path.join(self.output_dir, f"{base_name}_hu_moments.png")
                plt.savefig(hu_path)
                plt.close()
                all_path.append(hu_path)
                print(f"Hu矩特征提取成功: {base_name}_hu_moments.png")
            except Exception as e:
                print(f"Hu矩特征提取失败: {e}")

        # HOG特征
        if img is not None:
            # 确保图像是二维的
            if len(img.shape) == 3:
                if img.shape[2] == 1:
                    img = img.squeeze()
                else:
                    img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            elif len(img.shape) != 2:
                print(f"图像维度不正确: {img.shape}")
                return all_path
                
            try:
                from skimage.feature import hog
                from skimage import exposure
                
                # 计算HOG特征
                hog_features, hog_image = hog(img, orientations=9, pixels_per_cell=(8, 8),
                                              cells_per_block=(2, 2), visualize=True, block_norm='L2-Hys')
                
                # 可视化HOG特征
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6), subplot_kw=dict(xticks=[], yticks=[]))
                ax1.imshow(img, cmap='gray')
                ax1.set_title('Original Image')
                ax2.imshow(hog_image, cmap='gray')
                ax2.set_title('HOG Features')
                plt.tight_layout()
                hog_path = os.path.join(self.output_dir, f"{base_name}_hog_features.png")
                plt.savefig(hog_path)
                plt.close()
                all_path.append(hog_path)
                print(f"HOG特征提取成功: {base_name}_hog_features.png")
            except Exception as e:
                print(f"HOG特征提取失败: {e}")

        # LBP纹理特征提取
        lbp_img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
        print("lbp_img.shape",lbp_img.shape)

        # 确保图像是二维的
        if lbp_img is not None:
            # 如果图像是三维的 (height, width, 1)，则转换为二维 (height, width)
            if len(lbp_img.shape) == 3 and lbp_img.shape[2] == 1:
                lbp_img = lbp_img.squeeze()  # 移除第三个维度
            elif len(lbp_img.shape) == 3 and lbp_img.shape[2] > 1:
                # 如果是多通道图像，转换为灰度图
                lbp_img = cv2.cvtColor(lbp_img, cv2.COLOR_BGR2GRAY)
            
            # 现在确保图像是二维的
            if len(lbp_img.shape) == 2:
                try:
                    radius = 3
                    n_points = 8 * radius
                    
                    lbp = local_binary_pattern(lbp_img, n_points, radius, method='uniform')
                    lbp_normalized = np.uint8(255*(lbp/np.max(lbp)) if np.max(lbp) > 0 else lbp)
                    lbp_color = cv2.cvtColor(lbp_normalized, cv2.COLOR_GRAY2BGR)
                    
                    output_path = os.path.join(self.output_dir, f"{base_name}_lbp.jpg")
                    cv2.imwrite(output_path, lbp_color)
                    all_path.append(output_path)
                    print(f"LBP纹理特征提取成功: {base_name}_lbp.jpg")
                except Exception as e:
                    print(f"LBP纹理特征提取失败: {e}")
            else:
                print(f"LBP纹理特征提取失败: 图像不是二维的，形状为 {lbp_img.shape}")
        else:
            print(f"LBP纹理特征提取失败: 图像为空")


        # Harris角点检测
        harris_img = img.copy()
        harris_corners = cv2.cornerHarris(np.float32(harris_img), 2, 3, 0.04)
        # 归一化并阈值
        harris_img_color = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
        harris_img_color[harris_corners > 0.01 * harris_corners.max()] = [0, 0, 255]
        cv2.imwrite(os.path.join(self.output_dir, f"{base_name}_harris.jpg"), harris_img_color)
        print(f"Harris角点特征提取成功: {base_name}.jpg")
        all_path.append(os.path.join(self.output_dir, f"{base_name}_harris.jpg"))

        # 提取Harris角点的高斯分布特征
        try:
            harris_gaussian = self.extract_harris_corner_gaussian_distribution(img_path)
            if harris_gaussian:
                print(f"Harris角点高斯分布提取成功")
        except Exception as e:
            print(f"Harris角点高斯分布提取失败: {e}")

        # Canny 边缘检测
        # 读取原始灰度图像（已在 extract_and_save 方法中加载）
        edge_img = img.copy()
        # 使用 Canny 算法提取边缘
        edges = cv2.Canny(edge_img, threshold1=100, threshold2=200)

        # 计算边缘密度特征
        total_pixels = img.shape[0] * img.shape[1]  # 总像素数
        edge_pixels = np.count_nonzero(edges)       # 边缘像素数
        edge_density = edge_pixels / total_pixels   # 边缘密度

        # 保存边缘密度信息到JSON文件
        import json
        density_data = {
            'image_path': img_path,
            'total_pixels': total_pixels,
            'edge_pixels': int(edge_pixels),
            'edge_density': float(edge_density)
        }

        density_json_path = os.path.join(self.output_dir, f"{base_name}_边缘密度.json")
        with open(density_json_path, 'w', encoding='utf-8') as f:
            json.dump(density_data, f, ensure_ascii=False, indent=2)
        print(f"边缘密度特征: {density_json_path}\n")
        print(f"边缘密度: {edge_density:.6f} ({edge_pixels}/{total_pixels})\n")

        # 保存结果图像
        output_path = os.path.join(self.output_dir, f"{base_name}_canny.jpg")
        success = cv2.imwrite(output_path, edges)

        all_path.append(output_path)
        if success:
            print(f"边缘特征提取成功: {base_name}_canny.jpg")
        else:
            print(f"无法保存边缘特征图像: {base_name}_canny.jpg")

        # # BRISK特征

        # # 通过调整参数来提高特征点检测的阈值
        # brisk = cv2.BRISK_create(thresh=30, octaves=3, patternScale=1.0)  # 默认thresh=30，可以调高到40-60
        # kp_brisk, des_brisk = brisk.detectAndCompute(img, None)

        # # 可以进一步过滤关键点，只保留响应值较高的点
        # if kp_brisk is not None and len(kp_brisk) > 0:
        #     # 过滤掉响应值较低的关键点，例如只保留响应值大于0.05的点
        #     kp_brisk = [kp for kp in kp_brisk if kp.response > 0.05]
            
        # brisk_img = cv2.drawKeypoints(img, kp_brisk, None, color=(255,0,0), flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS)
        # cv2.imwrite(os.path.join(self.output_dir, f"{base_name}_brisk.jpg"), brisk_img)
        # print(f"BRISK特征提取成功: {base_name}.jpg")
        # all_path.append(os.path.join(self.output_dir, f"{base_name}_brisk.jpg"))
        # # np.save(os.path.join(self.output_dir, f"{base_name}_brisk_kp.npy"), np.array([k.pt for k in kp_brisk]))
        # # np.save(os.path.join(self.output_dir, f"{base_name}_brisk_des.npy"), des_brisk)


        # # SIFT角点特征

        # # 重新读取图像为灰度图（确保未被修改）
        # img_sift = img.copy()
        # if img_sift is None:
        #     print(f"SIFT: 图像未找到 - {img_path}")

        # sift = cv2.SIFT_create(nfeatures=0,           # 要保留的特征点数量(0表示不限制)
        #                       contrastThreshold=0.04,   # 对比度阈值，可以提高到0.06-0.1
        #                       edgeThreshold=10,         # 边缘阈值
        #                       sigma=1.6)                # 高斯模糊标准差

        # # 检测关键点并计算描述子
        # keypoints, descriptors = sift.detectAndCompute(img_sift, None)

        # # 如果检测到了关键点，可以进一步过滤低质量的点
        # if keypoints is not None and len(keypoints) > 0:
        #     # 根据响应值过滤关键点，只保留响应值较高的点
        #     min_response = 0.02  # 可以根据需要调整
        #     keypoints = [kp for kp in keypoints if kp.response > min_response]
        #     print(f"SIFT检测到的关键点数量: {len(keypoints)}")
            
        # if keypoints is None or len(keypoints) == 0:
        #     print(f"SIFT: 未检测到关键点 - {img_path}")
        # else:
        #     # 可视化关键点
        #     sift_img = cv2.drawKeypoints(img_sift, keypoints, None,
        #                                 flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS,
        #                                 color=(255, 0, 0))  # 蓝色关键点
        #     # 保存可视化图像
        #     cv2.imwrite(os.path.join(self.output_dir, f"{base_name}_sift.jpg"), sift_img)
        #     all_path.append(os.path.join(self.output_dir, f"{base_name}_sift.jpg"))
        #     print(f"SIFT特征提取成功: {base_name}_sift.jpg")

        # # ORB 特征提取

        # # 创建 ORB 检测器并调整参数降低阈值
        # orb = cv2.ORB_create(nfeatures=500,          # 要检测的特征点数量
        #                     scaleFactor=1.2,         # 金字塔缩放比例
        #                     nlevels=8,               # 金字塔层数
        #                     edgeThreshold=31,        # 边缘阈值
        #                     firstLevel=0,            # 金字塔第一层
        #                     WTA_K=2,                 # 生成描述子时随机选择的点数
        #                     scoreType=cv2.ORB_HARRIS_SCORE,  # 评分方法
        #                     patchSize=31,            # patch大小
        #                     fastThreshold=10)        # FAST检测器阈值，可以降低到7-10

        # # 检测关键点并计算描述子
        # keypoints, descriptors = orb.detectAndCompute(img, None)

        # # 可以进一步降低阈值要求，保留更多特征点
        # if keypoints is not None and len(keypoints) > 0:
        #     # 降低响应值过滤阈值，保留更多特征点
        #     min_response = 0.001  # 降低响应值阈值
        #     keypoints = [kp for kp in keypoints if kp.response > min_response]
        #     print(f"ORB检测到的关键点数量: {len(keypoints)}")

        # if keypoints is None or len(keypoints) == 0:
        #     print(f"ORB: 未检测到关键点 - {base_name}")
        # else:
        #     # 可视化关键点
        #     orb_img = cv2.drawKeypoints(img, keypoints, None,
        #                             flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS,
        #                             color=(255, 0, 0))  # 蓝色关键点

        #     # 保存可视化图像
        #     output_path = os.path.join(self.output_dir, f"{base_name}_orb.jpg")
        #     success = cv2.imwrite(output_path, orb_img)
        #     all_path.append(output_path)

        #     if success:
        #         print(f"ORB特征提取成功: {base_name}_orb.jpg")
        #     else:
        #         print(f"无法保存 ORB 特征图像: {base_name}_orb.jpg")
        # # return all_path

        # YOLO目标检测和长宽比计算
        try:
            objects_info = self.detect_objects_and_calculate_ratios(img_path)
            if objects_info:
                print(f"YOLO检测完成，共检测到 {len(objects_info)} 个目标")
        except Exception as e:
            print(f"YOLO检测或长宽比计算失败: {e}")

        # 提取目标四元数特征
        try:
            quaternion_features = self.extract_object_quaternions(img_path)
            if quaternion_features:
                print(f"成功提取 {len(quaternion_features)} 个目标的四元数特征")
        except Exception as e:
            print(f"四元数特征提取失败: {e}")

        try:
            moment_quaternions = self.extract_moment_quaternions(img_path)
            if moment_quaternions:
                print(f"成功提取 {len(moment_quaternions)} 个目标的矩四元数特征")
        except Exception as e:
            print(f"矩四元数特征提取失败: {e}")

        # 返回所有生成的文件路径
        return all_path

    @staticmethod
    def process_FeatureExtractor(inputs: Union[str, List[str]], output_dir: str, results_files):
        processor = FeatureExtractor(output_dir)
        all_results = []

        if isinstance(inputs, str):
            if os.path.isfile(inputs):
                print(f"处理单个文件: {inputs}")
                result = processor.extract_and_save(inputs, results_files)
                if result:
                    all_results.extend(result)
                return all_results
            elif os.path.isdir(inputs):
                print(f"处理文件夹: {inputs}")
                for root, dirs, files in os.walk(inputs):
                    for file in files:
                        if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')):
                            img_path = os.path.join(root, file)
                            print(f"处理图像: {img_path}")
                            result = processor.extract_and_save(img_path, results_files)
                            if result:
                                all_results.extend(result)
                return all_results
            else:
                print("输入路径既不是文件也不是文件夹！")
                return []
        elif isinstance(inputs, list):
            for img_path in inputs:
                if os.path.isfile(img_path):
                    print(f"处理列表中的文件: {img_path}")
                    result = processor.extract_and_save(img_path, results_files)
                    if result:
                        all_results.extend(result)
                else:
                    print(f"跳过不存在的文件: {img_path}")
            return all_results
        else:
            print("输入类型不支持！")
            return []

class FeatureSelector:
    """
    特征识别有效性筛选类，支持多种筛选方法，并可自动选出最优图片
    """
    def __init__(self, min_keypoints=10, min_response=0.01, min_area_ratio=0.3):
        """
        :param min_keypoints: 有效特征点最小数量
        :param min_response: 有效特征点最小响应值（适用于SIFT/ORB等）
        :param min_area_ratio: 特征点分布的最小覆盖面积比例（0~1）
        """
        self.min_keypoints = min_keypoints
        self.min_response = min_response
        self.min_area_ratio = min_area_ratio

    def score_image(self, img_path, method='sift'):
        """
        计算单张图片的特征有效性得分（可自定义加权）
        """
        img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
        if img is None:
            return 0
        if method == 'sift' or method == 'asift':
            detector = cv2.SIFT_create()
        elif method == 'brisk':
            detector = cv2.BRISK_create()
        else:
            return 0

        kp, des = detector.detectAndCompute(img, None)
        if kp is None or len(kp) == 0 or des is None:
            return 0

        # 评分项
        num_kp = len(kp)
        max_response = max([point.response for point in kp if hasattr(point, 'response')], default=0)
        # 分布面积
        points = np.array([k.pt for k in kp])
        x_min, y_min = points.min(axis=0)
        x_max, y_max = points.max(axis=0)
        area = (x_max - x_min) * (y_max - y_min)
        img_area = img.shape[0] * img.shape[1]
        area_ratio = area / img_area if img_area > 0 else 0

        # 你可以自定义加权方式
        score = (
            num_kp * 1.0 +          # 特征点数量
            max_response * 100.0 +  # 响应值
            area_ratio * 100.0      # 分布面积比例
        )
        return score

    def select_best_image(self, folder, method='sift'):
        """
        在文件夹中筛选出特征识别有效性最强的图片
        :param folder: 文件夹路径
        :param method: 特征类型
        :return: 最优图片路径
        """
        best_score = -1
        best_img = None
        for file in os.listdir(folder):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')):
                img_path = os.path.join(folder, file)
                score = self.score_image(img_path, method=method)
                if score > best_score:
                    best_score = score
                    best_img = img_path
        if best_img:
            print(f"最优图片: {best_img}，得分: {best_score}")
        else:
            print("未找到有效图片")
        return best_img



    @staticmethod
    def process_inputs(inputs: Union[str, List[str]], output_dir: str):
        extractor = FeatureExtractor(output_dir)
        if isinstance(inputs, str):
            if os.path.isfile(inputs):
                extractor.extract_and_save(inputs)
            elif os.path.isdir(inputs):
                for file in os.listdir(inputs):
                    if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')):
                        img_path = os.path.join(inputs, file)
                        extractor.extract_and_save(img_path)
            else:
                print("输入路径既不是文件也不是文件夹！")
        elif isinstance(inputs, list):
            for img_path in inputs:
                extractor.extract_and_save(img_path)
        else:
            print("输入类型不支持！")