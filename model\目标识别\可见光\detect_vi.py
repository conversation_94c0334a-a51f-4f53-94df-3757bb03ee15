# yolov8_detector.py

import cv2
import torch
import numpy as np
from ultralytics import YOLO

class CustomYOLOv8Detector:
    """
    自定义YOLOv8目标检测器类
    可在其他Python文件中调用此类进行目标检测
    """
    
    def __init__(self, model_path, conf_threshold=0.5, iou_threshold=0.4):
        """
        初始化检测器
        
        Args:
            model_path (str): YOLOv8模型文件路径
            conf_threshold (float): 置信度阈值，默认0.5
            iou_threshold (float): IOU阈值，默认0.4
        """
        self.model = YOLO(model_path)
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold
        self.class_names = self.model.names if hasattr(self.model, 'names') else None
    
    def detect_image(self, image_path):
        """
        对单张图像进行目标检测
        
        Args:
            image_path (str): 图像文件路径
            
        Returns:
            tuple: (results, output_image) 检测结果和标注后的图像
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 执行检测
        results = self._perform_detection(image)
        
        # 绘制检测结果
        output_image = self._draw_detections(image, results[0])
        
        return results[0], output_image
    
    def detect_frame(self, frame):
        """
        对视频帧或图像数组进行目标检测
        
        Args:
            frame (numpy.ndarray): 图像数组(BGR格式)
            
        Returns:
            tuple: (results, output_image) 检测结果和标注后的图像
        """
        # 执行检测
        results = self._perform_detection(frame)
        
        # 绘制检测结果
        output_image = self._draw_detections(frame, results[0])
        
        return results[0], output_image
    
    def _perform_detection(self, image):
        """
        执行实际的目标检测
        
        Args:
            image: 输入图像
            
        Returns:
            检测结果
        """
        results = self.model.predict(
            source=image,
            conf=self.conf_threshold,
            iou=self.iou_threshold,
            verbose=False
        )
        return results
    
    def _draw_detections(self, image, results):
        """
        在图像上绘制检测结果
        
        Args:
            image: 原始图像
            results: 检测结果
            
        Returns:
            标注后的图像
        """
        # 复制图像避免修改原图
        output_image = image.copy()
        
        # 获取检测框
        boxes = results.boxes
        
        if boxes is not None and len(boxes) > 0:
            # 遍历所有检测到的目标
            for box in boxes:
                # 获取边界框坐标
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                
                # 获取置信度
                confidence = box.conf[0].cpu().numpy()
                
                # 获取类别ID
                class_id = int(box.cls[0].cpu().numpy())
                
                # 获取类别名称
                class_name = self.class_names[class_id] if self.class_names else f"Class {class_id}"
                
                # 绘制边界框
                cv2.rectangle(output_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # 准备标签文本
                label = f"{class_name} {confidence:.2f}"
                
                # 计算文本尺寸
                (text_width, text_height), baseline = cv2.getTextSize(
                    label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2
                )
                
                # 绘制标签背景
                cv2.rectangle(
                    output_image,
                    (x1, y1 - text_height - baseline),
                    (x1 + text_width, y1),
                    (0, 255, 0),
                    -1
                )
                
                # 绘制标签文本
                cv2.putText(
                    output_image,
                    label,
                    (x1, y1 - baseline),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.5,
                    (0, 0, 0),
                    2
                )
        
        return output_image
    
    def set_confidence_threshold(self, threshold):
        """
        设置置信度阈值
        
        Args:
            threshold (float): 置信度阈值 (0.0 - 1.0)
        """
        self.conf_threshold = max(0.0, min(1.0, threshold))
    
    def set_iou_threshold(self, threshold):
        """
        设置IOU阈值
        
        Args:
            threshold (float): IOU阈值 (0.0 - 1.0)
        """
        self.iou_threshold = max(0.0, min(1.0, threshold))
    
    def get_detected_objects(self, results):
        """
        获取检测到的对象信息列表
        
        Args:
            results: YOLO检测结果
            
        Returns:
            list: 包含检测对象信息的列表
        """
        objects = []
        boxes = results.boxes
        
        if boxes is not None:
            for box in boxes:
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                confidence = float(box.conf[0].cpu().numpy())
                class_id = int(box.cls[0].cpu().numpy())
                class_name = self.class_names[class_id] if self.class_names else f"Class {class_id}"
                
                objects.append({
                    'class_id': class_id,
                    'class_name': class_name,
                    'confidence': confidence,
                    'bbox': [int(x1), int(y1), int(x2), int(y2)]
                })
        
        return objects

# 使用示例 (在其他文件中):
"""
from yolov8_detector import CustomYOLOv8Detector

# 创建检测器实例
detector = CustomYOLOv8Detector(
    model_path="path/to/your/yolov8_model.pt",
    conf_threshold=0.5,
    iou_threshold=0.4
)

# 检测图像
results, output_image = detector.detect_image("path/to/image.jpg")

# 获取检测对象信息
objects = detector.get_detected_objects(results)
for obj in objects:
    print(f"类别: {obj['class_name']}, 置信度: {obj['confidence']:.2f}, 位置: {obj['bbox']}")

# 保存结果图像
cv2.imwrite("output.jpg", output_image)

# 调整参数后重新检测
detector.set_confidence_threshold(0.7)
results, output_image = detector.detect_image("path/to/image.jpg")

detect_image(): 检测图像文件
detect_frame(): 检测图像数组(适用于视频处理)
set_confidence_threshold(): 动态调整置信度阈值
set_iou_threshold(): 动态调整IOU阈值
get_detected_objects(): 获取结构化的检测结果


"""




