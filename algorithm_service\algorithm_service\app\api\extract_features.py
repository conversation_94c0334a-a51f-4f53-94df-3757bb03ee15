from flask import Blueprint, request, jsonify, send_file
from io import BytesIO
from model.特征提取.可见光.feature_img import *

from PIL import Image
import cv2,os
import numpy as np
from datetime import datetime
import uuid

extract_features_bp = Blueprint('extract_features', __name__)

@extract_features_bp.route('/visible_light', methods=['POST'])
def extract_features_visible_light():
    """
    可见光特征提取接口

    """
    try:
        # 检查请求中是否包含文件
        if 'image' not in request.files:
            return jsonify({
                "status": "error",
                "message": "请求中缺少图像文件"
            }), 400

        # 获取上传的图像文件
        file = request.files['image']

        # 检查文件是否有文件名
        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "未选择图像文件"
            }), 400

        # 读取图像
        img = Image.open(file.stream).convert('RGB')

        # 将PIL图像转换为OpenCV格式（BGR）
        img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

        # 图像锐化处理
        # 方法1：使用拉普拉斯算子锐化
        kernel_laplacian = np.array([[0, -1, 0],
                                   [-1, 5, -1],
                                   [0, -1, 0]])
        sharpened_img = cv2.filter2D(img_cv, -1, kernel_laplacian)

        # 方法2：Unsharp Masking锐化（可选，注释掉了）
        # gaussian_blur = cv2.GaussianBlur(img_cv, (0, 0), 2.0)
        # sharpened_img = cv2.addWeighted(img_cv, 1.5, gaussian_blur, -0.5, 0)

        # 将OpenCV图像转换回PIL格式
        img_processed = Image.fromarray(cv2.cvtColor(sharpened_img, cv2.COLOR_BGR2RGB))


        # 将处理后的图像保存到内存中
        buffer = BytesIO()
        img_processed.save(buffer, format='JPEG')
        buffer.seek(0)

        # 返回处理后的图像文件
        return send_file(
            buffer,
            mimetype='image/jpeg',
        )

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

#可以按照可见光、红外和SAR分类写三个服务函数

@extract_features_bp.route('/sharpen', methods=['POST'])
def sharpen_image():
    """
    图像锐化接口
    接收：图像文件
    返回：锐化后的图像文件
    """
    try:
        # 检查请求中是否包含文件
        if 'image' not in request.files:
            return jsonify({
                "status": "error",
                "message": "请求中缺少图像文件"
            }), 400

        # 获取上传的图像文件
        file = request.files['image']

        # 检查文件是否有文件名
        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "未选择图像文件"
            }), 400

        # 读取图像
        img = Image.open(file.stream).convert('RGB')

        # 将PIL图像转换为OpenCV格式（BGR）
        img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

        # 获取锐化强度参数（可选，默认为中等强度）
        intensity = request.form.get('intensity', 'medium')

        if intensity == 'light':
            # 轻度锐化
            kernel = np.array([[0, -0.5, 0],
                             [-0.5, 3, -0.5],
                             [0, -0.5, 0]])
        elif intensity == 'strong':
            # 强度锐化
            kernel = np.array([[0, -1, 0],
                             [-1, 6, -1],
                             [0, -1, 0]])
        else:  # medium
            # 中等锐化（默认）
            kernel = np.array([[0, -1, 0],
                             [-1, 5, -1],
                             [0, -1, 0]])

        # 应用锐化滤波器
        sharpened_img = cv2.filter2D(img_cv, -1, kernel)

        # 将OpenCV图像转换回PIL格式
        img_processed = Image.fromarray(cv2.cvtColor(sharpened_img, cv2.COLOR_BGR2RGB))

        # 将处理后的图像保存到内存中
        buffer = BytesIO()
        img_processed.save(buffer, format='JPEG')
        buffer.seek(0)

        # 返回处理后的图像文件
        return send_file(
            buffer,
            mimetype='image/jpeg',
            as_attachment=False,
            download_name='sharpened_image.jpg'
        )

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@extract_features_bp.route('/infrared', methods=['POST'])
def extract_features_infrared():
    """
    红外图像特征提取接口
    """
    try:
        # 检查请求中是否包含文件
        if 'image' not in request.files:
            return jsonify({
                "status": "error",
                "message": "请求中缺少图像文件"
            }), 400

        # 获取上传的图像文件
        file = request.files['image']

        # 检查文件是否有文件名
        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "未选择图像文件"
            }), 400

        # 读取图像
        img = Image.open(file.stream).convert('RGB')

        # TODO: 添加红外图像特征提取算法
        # 这里可以添加针对红外图像的特殊处理逻辑

        # 将处理后的图像保存到内存中
        buffer = BytesIO()
        img.save(buffer, format='JPEG')
        buffer.seek(0)

        # 返回处理后的图像文件
        return send_file(
            buffer,
            mimetype='image/jpeg',
        )

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500






@extract_features_bp.route('/multiple', methods=['POST'])
def extract_multiple_features_base64():
    """
    多图像特征提取接口
    将结果保存到服务器文件夹并返回访问信息
    """
    temp_input_file = None
    try:
        if 'image' not in request.files:
            return jsonify({
                "status": "error",
                "message": "请求中缺少图像文件"
            }), 400

        file = request.files['image']

        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "未选择图像文件"
            }), 400

        # 生成唯一的文件夹名称，避免并发冲突
        unique_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建临时文件夹来存储输入图像和结果（使用唯一标识符）
        base_path = "Results/可见光/特征提取模块"
        temp_folder = f"{base_path}/过程暂存_{timestamp}_{unique_id[:8]}"
        folder_name = f"{base_path}/图像预处理_{timestamp}_{unique_id[:8]}"  # 预处理结果文件夹
        folder_name_2 = f"{base_path}/特征提取_{timestamp}_{unique_id[:8]}"    # 特征提取结果文件夹

        # 创建必要的文件夹
        os.makedirs(temp_folder, exist_ok=True)
        os.makedirs(folder_name, exist_ok=True)
        os.makedirs(folder_name_2, exist_ok=True)

        # 保存上传的图像到临时文件
        original_filename = file.filename
        file_extension = os.path.splitext(original_filename)[1] if original_filename else '.jpg'
        temp_input_file = os.path.join(temp_folder, f"input_image{file_extension}")
        file.save(temp_input_file)

        result_files = []

        # 调用图像预处理
        print(f"开始预处理图像: {temp_input_file}")
        ImageProcessor.process_ImageProcessor(temp_input_file, folder_name)

        # 调用特征提取（修正参数传递）
        print(f"开始特征提取，输入文件夹: {folder_name}")
        feature_results = FeatureExtractor.process_FeatureExtractor(folder_name, folder_name_2, result_files)

        # 获取生成的文件列表
        generated_files = []

        # 收集预处理结果文件
        if os.path.exists(folder_name):
            for filename in os.listdir(folder_name):
                if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')):
                    generated_files.append({
                        "type": "preprocessed",
                        "filename": filename,
                        "path": os.path.join(folder_name, filename)
                    })

        # 收集特征提取结果文件
        if os.path.exists(folder_name_2):
            for filename in os.listdir(folder_name_2):
                if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.json', '.txt')):
                    generated_files.append({
                        "type": "features",
                        "filename": filename,
                        "path": os.path.join(folder_name_2, filename)
                    })

        return jsonify({
            "status": "success",
            "message": "处理完成",
            "original_filename": original_filename,
            "preprocessed_folder": folder_name,
            "features_folder": folder_name_2,
            "generated_files": generated_files,
            "total_files": len(generated_files),
            "processing_id": unique_id[:8]
        })

    except Exception as e:
        # 清理临时文件
        if temp_input_file and os.path.exists(temp_input_file):
            try:
                os.remove(temp_input_file)
                # 尝试删除临时文件夹（如果为空）
                temp_dir = os.path.dirname(temp_input_file)
                if os.path.exists(temp_dir) and not os.listdir(temp_dir):
                    os.rmdir(temp_dir)
            except:
                pass  # 忽略清理错误

        return jsonify({
            "status": "error",
            "message": f"处理失败: {str(e)}"
        }), 500